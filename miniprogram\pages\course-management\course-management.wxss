/**
 * course-management.wxss - 活动管理页面样式文件
 *
 * 优化说明：
 * - 统一使用rpx单位，确保响应式布局
 * - 移除重复的选择器和属性声明
 * - 按功能模块组织样式规则
 * - 优化性能和可维护性
 */

/* ==================== 基础布局样式 ==================== */

page, .page {
  height: 100%;
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  background-color: #f5f5f5;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  overflow-x: hidden;
}

/* ==================== 顶部区域样式 ==================== */

.top-section {
  flex-shrink: 0;
  width: 100%;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 100;
}

.top-tabs-section {
  position: relative;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.02);
  padding: 0 32rpx 8rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
}

.top-tabs-section:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}

.custom-top-tabs {
  background-color: transparent;
  border: none;
  font-size: 32rpx;
  margin: 0;
  width: 100%;
  height: auto;
}

.custom-top-tabs .t-tabs__nav {
  padding: 0;
  height: auto;
  border-bottom: none;
  display: flex;
  align-items: center;
  background: transparent;
}

.custom-top-tabs .t-tabs__item {
  font-size: 32rpx !important;
  font-weight: 500;
  padding: 28rpx 40rpx !important;
  height: auto;
  line-height: 1.4;
  min-height: 88rpx;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 0;
  background: transparent !important;
  border-bottom: 6rpx solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #666666 !important;
  position: relative;
}

.custom-top-tabs .t-tabs__item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 0;
  height: 4rpx;
  background: linear-gradient(90deg, transparent, #0052d9, transparent);
  transform: translateX(-50%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.custom-top-tabs .t-tabs__item--active {
  color: #0052d9 !important;
  font-weight: 600 !important;
  border-bottom-color: #0052d9 !important;
  background: transparent !important;
  text-shadow: 0 0 2rpx rgba(0, 82, 217, 0.1);
}

.custom-top-tabs .t-tabs__item--active::before {
  width: 60%;
}

.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover {
  color: #333333 !important;
  background: transparent !important;
  border-bottom-color: rgba(0, 82, 217, 0.3) !important;
}

.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover::before {
  width: 30%;
}

.custom-top-tabs .t-tabs__track {
  display: none;
}

/* ==================== 筛选区域样式 ==================== */

.filter-section {
  width: 100%;
  background: transparent;
  border: none;
  padding: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  transition: all 0.2s ease;
  overflow: visible;
  z-index: 90;
  position: relative;
  margin-bottom: 32rpx;
}

/* ==================== 活动列表样式 ==================== */

.course-list, .template-list {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
}

.course-content, .template-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;
}

.timeline-date {
  margin: 16rpx;
  font-size: 28rpx;
  color: #0052d9;
  font-weight: bold;
  position: relative;
  padding-left: 32rpx;
}

.timeline-date::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 16rpx;
  height: 16rpx;
  background: #0052d9;
  border-radius: 50%;
  transform: translateY(-50%);
}

/* ==================== 活动/模板卡片样式 ==================== */

.course-card, .template-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  transition: all 0.2s ease;
}

.course-card:active, .template-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.course-card.slide-in {
  animation: slide-in-up 0.6s ease-out;
}

@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(40rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.course-header, .template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  gap: 16rpx;
}

.course-title, .template-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ==================== 状态标签样式 ==================== */

.course-status {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  flex-shrink: 0;
  white-space: nowrap;
}

/* 状态标签颜色 */
.course-status.available { background-color: #e8f5e8; color: #52c41a; }
.course-status.booked { background-color: #e6f3ff; color: #0052d9; }
.course-status.full { background-color: #fff2e8; color: #fa8c16; }
.course-status.ended { background-color: #f0f0f0; color: #888; }
.course-status.online { background-color: #e8f5e8; color: #52c41a; }
.course-status.offline { background-color: #fff2e8; color: #fa8c16; }
.course-status.no-status { background-color: #f0f0f0; color: #999; }
.course-status.template-status { background-color: #e6f3ff; color: #1890ff; }

/* ==================== 信息项目样式 ==================== */

.course-info-list, .template-info-list {
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  color: #666;
  gap: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item t-icon {
  color: #0052d9;
  flex-shrink: 0;
}

.info-item text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ==================== 卡片底部操作区域 ==================== */

.course-footer, .template-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
  gap: 16rpx;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding-bottom: 4rpx;
  min-width: 0;
}

.action-buttons .t-button {
  flex-shrink: 0;
  min-width: 120rpx;
  max-width: 160rpx;
}

.edit-disabled-btn {
  background-color: #f0f0f0 !important;
  border-color: #e0e0e0 !important;
  color: #bbb !important;
  cursor: not-allowed;
}

/* ==================== 已预约学员区域样式 ==================== */

.booked-students-section {
  margin: 24rpx 0;
  border: 1rpx solid #f0f0f0;
  border-radius: 16rpx;
  overflow: hidden;
}

.collapse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: #f8f8f8;
  cursor: pointer;
  font-size: 28rpx;
  color: #333;
}

.collapse-content {
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  padding: 0 32rpx;
}

.student-list {
  padding: 16rpx 0;
}

.student-item {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
  font-size: 28rpx;
  color: #666;
  justify-content: space-between;
}

.student-item t-icon {
  margin-right: 16rpx;
  color: #0052d9;
}

.student-name {
  flex: 1;
  margin-right: 16rpx;
}

.remove-student-icon {
  flex-shrink: 0;
  margin-left: auto;
  padding: 8rpx;
  cursor: pointer;
  border-radius: 8rpx;
  transition: background-color 0.2s;
}

.remove-student-icon:hover {
  background-color: rgba(227, 77, 89, 0.1);
}

.no-students {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  padding: 40rpx 0;
}

/* ==================== 搜索和筛选样式 ==================== */

.search-actions-section {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: nowrap;
  justify-content: flex-start;
  box-sizing: border-box;
  overflow: visible;
  min-height: 64rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 16rpx;
  border: 2rpx solid #e7e7e7;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.04);
}

.search-actions-section.collapsed {
  justify-content: flex-start;
}

.collapsed-layout {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.search-actions-section.expanded {
  justify-content: flex-start;
}

.expanded-layout {
  flex: 1;
  height: 100%;
  display: flex;
  min-width: 0;
}

.search-icon-only {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  background: #ffffff;
  border-radius: 8rpx;
  border: 2rpx solid #d9d9d9;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.search-icon-only:active {
  background: #f0f8ff;
  border-color: #0052d9;
  transform: scale(0.95);
}

.search-toggle-icon {
  color: #666666;
  font-size: 28rpx;
}

.search-input-container {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 8rpx;
  padding: 12rpx 20rpx;
  border: 2rpx solid #d9d9d9;
  transition: all 0.2s ease;
  animation: searchExpand 0.3s ease-out;
  width: 100%;
  flex: 1;
  box-sizing: border-box;
  height: 64rpx;
}

.search-input-container:focus-within {
  border-color: #0052d9;
  box-shadow: 0 0 0 4rpx rgba(0, 82, 217, 0.1);
}

.search-icon {
  color: #999999;
  margin-right: 12rpx;
  flex-shrink: 0;
  font-size: 28rpx;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 28rpx;
  color: #333333;
  background: transparent;
  min-width: 0;
  height: 40rpx;
  line-height: 40rpx;
}

.search-input::placeholder {
  color: #999999;
  font-size: 28rpx;
}

.clear-icon,
.collapse-icon {
  color: #999999;
  margin-left: 12rpx;
  flex-shrink: 0;
  cursor: pointer;
  font-size: 28rpx;
  padding: 8rpx;
  border-radius: 8rpx;
  transition: all 0.2s ease;
}

.clear-icon:active,
.collapse-icon:active {
  color: #0052d9;
  background: rgba(0, 82, 217, 0.1);
  transform: scale(0.95);
}

@keyframes searchExpand {
  from {
    opacity: 0;
    transform: scaleX(0.8);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}

.filter-dropdown-trigger {
  width: 240rpx;
  flex-shrink: 0;
  font-size: 28rpx;
}

.filter-button {
  height: 64rpx;
  padding: 0 16rpx 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8rpx;
  background-color: #ffffff;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  transition: all 0.2s ease;
  cursor: pointer;
  box-sizing: border-box;
}

.filter-button:hover {
  background-color: #f8f9fa;
  border-color: #0052d9;
}

.filter-button:active {
  background-color: #e6f4ff;
  transform: scale(0.98);
}

.filter-text {
  font-weight: 500;
  color: #333333;
  font-size: 28rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.filter-arrow {
  color: #999999;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.filter-arrow.rotated {
  transform: rotate(180deg);
}

.actions-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-shrink: 0;
  margin-left: auto;
  max-width: 70vw;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.actions-container::-webkit-scrollbar {
  display: none;
}

.actions-container .t-button {
  height: 64rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  flex-shrink: 0;
  white-space: nowrap;
  min-width: 120rpx;
  max-width: 160rpx;
}

/* ==================== 筛选菜单样式 ==================== */

.filter-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 400rpx;
}

.filter-menu {
  background: #fff;
  border-radius: 32rpx;
  box-shadow: 0 24rpx 96rpx rgba(0, 0, 0, 0.2);
  overflow: hidden;
  min-width: 480rpx;
  max-width: 640rpx;
  animation: filterMenuIn 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 2rpx solid rgba(0, 0, 0, 0.05);
}

@keyframes filterMenuIn {
  0% {
    opacity: 0;
    transform: translateY(-40rpx) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.filter-menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 48rpx 64rpx;
  font-size: 28rpx;
  color: #333333;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 2rpx solid #f5f5f5;
}

.filter-menu-item:last-child {
  border-bottom: none;
}

.filter-menu-item:hover {
  background-color: #f8f9fa;
}

.filter-menu-item.active {
  background-color: #e6f4ff;
  color: #0052d9;
  font-weight: 500;
}

.filter-menu-item-text {
  flex: 1;
  font-size: 28rpx;
}

.filter-menu-item-check {
  color: #0052d9;
  margin-left: 32rpx;
}


/* ==================== 批量操作样式 ==================== */

.batch-actions-bar {
  padding: 40rpx 48rpx;
  border-radius: 32rpx;
  margin-bottom: 32rpx;
  gap: 48rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9ff;
  border: 4rpx solid #e1f0ff;
  box-shadow: 0 8rpx 32rpx rgba(0, 82, 217, 0.1);
  animation: slide-down 0.3s ease-out;
}

@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-80rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.batch-info {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex-shrink: 0;
}

.batch-buttons {
  gap: 24rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  justify-content: flex-end;
}

.batch-buttons .t-button {
  height: 112rpx;
  padding: 0 32rpx;
  font-size: 52rpx;
  width: 240rpx;
  border-radius: 24rpx;
  font-weight: 500;
  flex-shrink: 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.course-card.batch-mode, .template-card.batch-mode {
  border: 8rpx solid #0052d9;
  box-shadow: 0 16rpx 48rpx rgba(0, 82, 217, 0.2);
  transform: translateY(-8rpx);
}

.course-title-row {
  display: flex;
  align-items: center;
  gap: 32rpx;
  flex: 1;
  overflow: hidden;
}

.course-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.custom-checkbox {
  width: 72rpx;
  height: 72rpx;
  border: 4rpx solid #d9d9d9;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.custom-checkbox.checked {
  background-color: #0052d9;
  border-color: #0052d9;
}

.check-icon {
  color: #ffffff;
}

/* ==================== 加载状态和提示样式 ==================== */

.loading-indicator {
  text-align: center;
  color: #888;
  font-size: 24rpx;
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #0052d9;
  animation: loading-dot-bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) {
  animation-delay: 0s;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes loading-dot-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.end-indicator {
  text-align: center;
  color: #b0b0b0;
  font-size: 24rpx;
  padding: 32rpx 0;
  letter-spacing: 4rpx;
}

/* ==================== 对话框和浮动按钮样式 ==================== */

.remove-student-dialog-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.remove-student-dialog-content text {
  display: block;
  margin-bottom: 16rpx;
}

.remove-student-dialog-content .student-name {
  font-weight: bold;
  color: #333;
}

.refund-checkbox-container {
  display: flex;
  align-items: center;
  margin-top: 32rpx;
}

.refund-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  transition: all 0.2s ease;
}

.refund-checkbox.checked {
  background-color: #0052d9;
  border-color: #0052d9;
}

.refund-checkbox .check-icon {
  color: #ffffff;
}

.refund-label {
  font-size: 28rpx;
  color: #333;
}

.countdown-dialog-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.countdown-dialog-content text {
  display: block;
  margin-bottom: 16rpx;
}

.countdown-dialog-content .course-name {
  font-weight: bold;
  color: #d9534f;
}

.back-to-today-fab {
  position: fixed;
  right: 64rpx;
  bottom: 240rpx;
  z-index: 100;
}

.back-to-today-fab .t-fab__btn {
  background-color: rgba(0, 0, 0, 0.5) !important;
  color: #ffffff !important;
}

/* ==================== 响应式布局调整 ==================== */

/* 小屏幕优化 */
@media (max-width: 375px) {
  .container {
    padding: 24rpx;
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  }

  .course-title, .template-title {
    font-size: 30rpx;
  }

  .info-item {
    font-size: 26rpx;
  }

  .actions-container .t-button,
  .batch-buttons .t-button {
    font-size: 24rpx;
    padding: 0 16rpx;
    min-width: 100rpx;
    max-width: 140rpx;
    height: 56rpx;
  }
}

/* ==================== 基础布局样式 ==================== */

/* 页面根元素和容器 */
page, .page {
  /*
   * height: 100%;
   * - `height` 属性设置元素的高度。
   * - `100%` 是一个相对单位，表示元素的高度将等于其父元素高度的100%。
   * - 这里让 `page` (页面根节点) 和 `.page` (通常是根节点的直接子元素) 高度撑满整个屏幕。
   */
  height: 100%;
}

/* 页面主容器 */
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  /*
   * padding: 32rpx;
   * - `padding` 是内边距，指元素边框与内容之间的空间。
   * - `32rpx` 是响应式像素单位，能根据屏幕宽度自适应，是小程序开发首选。
   *   常见取值有 16rpx, 24rpx, 32rpx, 40rpx 等，形成节奏感。
   */
  padding: 32rpx;
  /*
   * padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
   * - `padding-bottom` 单独设置底部的内边距。
   * - `calc()` 是CSS函数，用于进行计算。
   * - `env(safe-area-inset-bottom)` 是一个很重要的CSS变量，用于获取设备安全区域的底部边距，
   *   主要是为了适配 iPhone X 等有底部“小黑条”的全面屏手机，防止内容被遮挡。
   */
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  background-color: #f5f5f5;
  /*
   * box-sizing: border-box;
   * - 这是非常重要的CSS属性，它改变了盒子模型的计算方式。
   * - 默认的 `content-box` 中，你设置的 `width` 和 `height` 只包含内容区，不包括 `padding` 和 `border`。
   * - `border-box` 中，`width` 和 `height` 则包含了内容、`padding` 和 `border`。这让布局计算更直观，
   *   比如你设置 `width: 100%`，它就是真的100%宽，不会因为加了 `padding` 而超出父容器。
   *   这和建筑师设计房间时，直接考虑房间的轴线尺寸或内空尺寸类似，而不是只算使用面积。
   */
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  overflow-x: hidden;
}

/* ==================== 顶部区域样式 ==================== */

.top-section {
  flex-shrink: 0;
  width: 100%;
  /*
   * margin-bottom: 16rpx;
   * - `margin` 是外边距，元素边框外的空间，用于控制元素间的距离。
   * - `16rpx` 遵循了 `8rpx` 的基本设计步进，是常用的间距值。
   */
  margin-bottom: 16rpx;
  position: relative;
  z-index: 100;
}

.top-tabs-section {
  position: relative;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  /*
   * border: 1rpx solid #f0f0f0;
   * - `border` 设置元素的边框。
   * - `1rpx` 在这里是合适的，虽然 `1px` 也能画出最细的线，但在高清屏上 `1rpx` 也能达到类似效果且符合统一单位的原则。
   */
  border: 1rpx solid #f0f0f0;
  /*
   * border-radius: 16rpx;
   * - `border-radius` 用于设置元素的圆角。
   * - `16rpx` 是一个中等大小的圆角，参考了 `profile` 页面的设计。常见值有 8rpx, 12rpx, 16rpx, 24rpx。
   */
  border-radius: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.02), 0 0 0 2rpx rgba(0, 0, 0, 0.01);
  padding: 0 32rpx 8rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1rpx solid #e7e7e7;
  z-index: 10;
}

.top-tabs-section:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04), 0 0 0 2rpx rgba(0, 82, 217, 0.08);
}

.custom-top-tabs {
  font-size: 32rpx; /* 大字体 - 主要标题 */
}

.custom-top-tabs .t-tabs__item {
  font-size: 32rpx !important; /* 大字体 - 主要标题 */
  font-weight: 500;
  /*
   * padding: 28rpx 40rpx !important;
   * - `padding` 的两个值分别代表上下的内边距和左右的内边距。
   * - `28rpx` 和 `40rpx` 提供了足够大的点击区域，提升用户体验。
   */
  padding: 28rpx 40rpx !important;
  line-height: 1.4;
  min-height: 88rpx;
  border-bottom: 6rpx solid transparent;
}

.custom-top-tabs .t-tabs__item::before {
  height: 4rpx;
}

.custom-top-tabs .t-tabs__item--active {
  border-bottom-color: #0052d9 !important;
}

/* ==================== 课程列表样式 ==================== */

.course-list {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
}

.timeline-date {
  /*
   * margin: 16rpx;
   * - `16rpx` 的外边距，让日期标题和周围元素保持呼吸感。
   */
  margin: 16rpx;
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #0052d9;
  font-weight: bold;
  position: relative;
  padding-left: 32rpx;
}

.timeline-date::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  /*
   * width: 16rpx; height: 16rpx;
   * - 定义了时间轴上的装饰性圆点的大小。
   */
  width: 16rpx;
  height: 16rpx;
  background: #0052d9;
  border-radius: 50%;
  transform: translateY(-50%);
}

/* ==================== 课程卡片样式 ==================== */

.course-card {
  background-color: #ffffff;
  /*
   * border-radius: 24rpx;
   * - 统一使用 `profile` 页面定义的 `24rpx` 大圆角，视觉上更柔和、现代。
   */
  border-radius: 24rpx;
  /*
   * padding: 24rpx;
   * - `24rpx` 的内边距，让卡片内容不显得拥挤。
   */
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  width: 100%;
  box-sizing: border-box;
  transition: all 0.2s ease;
}

.course-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.course-card.slide-in {
  animation: slide-in-up 0.6s ease-out;
}

@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(40rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.course-header {
  margin-bottom: 16rpx;
  gap: 16rpx;
}

.course-title {
  font-size: 32rpx; /* 大字体 - 主要标题 */
  font-weight: 600;
  color: #333;
}

/* ==================== 课程状态标签样式 ==================== */

.course-status {
  /*
   * padding: 8rpx 16rpx;
   * - `8rpx` 上下内边距, `16rpx` 左右内边距。
   */
  padding: 8rpx 16rpx;
  /*
   * border-radius: 8rpx;
   * - 小圆角，适合标签这种小型组件。
   */
  border-radius: 8rpx;
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  font-weight: 500;
}

/* ==================== 课程信息项目样式 ==================== */

.course-info-list {
  margin-bottom: 20rpx;
}

.info-item {
  margin-bottom: 8rpx;
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #666;
  gap: 16rpx;
}

/* ==================== 课程卡片底部样式 ==================== */

.course-footer {
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
  gap: 16rpx;
}

.action-buttons {
  gap: 12rpx;
  padding-bottom: 4rpx;
}

.action-buttons .t-button {
  min-width: 120rpx;
  max-width: 160rpx;
}

/* ==================== 模板列表样式 ==================== */

.template-list {
  flex: 1;
  min-height: 0;
  margin-bottom: 32rpx;
  width: 100%;
  box-sizing: border-box;
}

.template-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  width: 100%;
  box-sizing: border-box;
}

.template-header {
  margin-bottom: 16rpx;
  gap: 12rpx;
}

.template-title {
  font-size: 32rpx; /* 大字体 - 主要标题 */
  font-weight: 600;
  color: #333;
}

.template-info-list {
  margin-bottom: 32rpx;
}

.template-footer {
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
  gap: 16rpx;
}

/* ==================== 已预约学员区域样式 ==================== */

.booked-students-section {
  /*
   * margin: 24rpx 0;
   * - 上下外边距 `24rpx`，左右为 `0`。
   */
  margin: 24rpx 0;
  border: 1rpx solid #f0f0f0;
  border-radius: 16rpx;
  overflow: hidden;
}

.collapse-header {
  padding: 24rpx 32rpx;
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

.collapse-content {
  border-top: 1rpx solid #f0f0f0;
  padding: 0 32rpx;
}

.student-list {
  padding: 16rpx 0;
}

.student-item {
  padding: 12rpx 0;
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

.student-item t-icon {
  margin-right: 16rpx;
}

.student-name {
  margin-right: 16rpx;
}

.remove-student-icon {
  padding: 8rpx;
  border-radius: 8rpx;
}

.no-students {
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  padding: 40rpx 0;
}

/* ==================== 筛选区域统一样式 ==================== */

.filter-dropdown-trigger {
  width: 240rpx;
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

.filter-button {
  height: 64rpx;
  padding: 0 16rpx 0 24rpx;
  gap: 8rpx;
  border-radius: 8rpx;
}

.filter-text {
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

/* ==================== 骨架屏及加载提示 ==================== */

.skeleton-card {
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  min-height: 240rpx;
}
.skeleton {
  border-radius: 12rpx;
}
.skeleton-text {
  height: 36rpx;
  margin-bottom: 16rpx;
}
.skeleton-block {
  height: 40rpx;
  width: 120rpx;
  margin-left: 16rpx;
}
.skeleton-btn {
  height: 56rpx;
  width: 120rpx;
  margin-right: 24rpx;
}

.loading-indicator {
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  padding: 40rpx 0;
  gap: 16rpx;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
}

.end-indicator {
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  padding: 32rpx 0;
  letter-spacing: 2rpx;
}

/* ==================== 搜索和操作区域统一样式 ==================== */

.search-actions-section {
  gap: 16rpx;
  min-height: 64rpx;
  border-radius: 16rpx;
  padding: 16rpx;
  margin-bottom: 16rpx; /* 添加统一的底部间距 */
}

.collapsed-layout {
  gap: 16rpx;
}

.search-icon-only {
  width: 64rpx;
  height: 64rpx;
  border-radius: 8rpx;
}

.search-toggle-icon {
  font-size: 28rpx; /* 中字体 - 图标大小 */
}

.search-input-container {
  border-radius: 8rpx;
  padding: 12rpx 20rpx;
  height: 64rpx;
}

.search-icon {
  margin-right: 12rpx;
  font-size: 28rpx; /* 中字体 - 图标大小 */
}

.search-input {
  font-size: 28rpx; /* 中字体 - 正文内容 */
  height: 40rpx;
  line-height: 40rpx;
}

.search-input::placeholder {
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

.clear-icon,
.collapse-icon {
  margin-left: 12rpx;
  font-size: 28rpx; /* 中字体 - 图标大小 */
  padding: 8rpx;
  border-radius: 8rpx;
}

.actions-container {
  gap: 16rpx;
}

.actions-container .t-button {
  height: 64rpx;
  padding: 0 24rpx;
  font-size: 28rpx; /* 中字体 - 正文内容 */
  min-width: 120rpx;
  max-width: 160rpx;
}

/* ==================== 批量操作栏统一样式 ==================== */

.batch-actions-bar {
  padding: 20rpx 24rpx;
  border-radius: 16rpx;
  margin-bottom: 16rpx; /* 统一间距为16rpx */
  gap: 24rpx;

  /* 单行布局 */
  display: flex;
  align-items: center;
  justify-content: space-between;

  /* 简洁的背景和边框 */
  background: #f8f9ff;
  border: 2rpx solid #e1f0ff;

  /* 轻微阴影 */
  box-shadow: 0 4rpx 16rpx rgba(0, 82, 217, 0.1);

  /* 出现动画 */
  animation: slide-down 0.3s ease-out;
}

@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.batch-info {
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #333;
  font-weight: 500;
  flex-shrink: 0; /* 防止文字被压缩 */
}

.batch-buttons {
  gap: 12rpx; /* 统一间距 */
  display: flex;
  align-items: center;
  flex-shrink: 0; /* 防止按钮被压缩 */
  justify-content: flex-end; /* 按钮右对齐，适应不同数量的按钮 */
}

.batch-buttons .t-button {
  height: 56rpx; /* 稍微减小高度适应单行布局 */
  padding: 0 16rpx; /* 减小内边距 */
  font-size: 26rpx; /* 稍微减小字体 */
  width: 120rpx; /* 固定宽度，确保所有按钮一样宽 */
  border-radius: 12rpx;
  font-weight: 500;
  flex-shrink: 0; /* 防止按钮被压缩 */

  /* 按钮阴影效果 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.course-card.batch-mode {
  border: 4rpx solid #0052d9;
  box-shadow: 0 8rpx 24rpx rgba(0, 82, 217, 0.2);
  transform: translateY(-4rpx);
}

.course-title-row {
  gap: 16rpx;
}

.custom-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
}

/* ==================== 对话框和浮动按钮 ==================== */

.remove-student-dialog-content {
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

.remove-student-dialog-content text {
  margin-bottom: 16rpx;
}

.refund-checkbox-container {
  margin-top: 32rpx;
}

.refund-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  margin-right: 16rpx;
}

.refund-label {
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

.countdown-dialog-content {
  font-size: 28rpx; /* 中字体 - 正文内容 */
}

.countdown-dialog-content text {
  margin-bottom: 16rpx;
}

.back-to-today-fab {
  right: 32rpx;
  bottom: 120rpx;
}

/* ==================== 响应式布局调整 (基于rpx，大部分媒体查询可简化或移除) ==================== */

/* 
 * 由于我们已经全面采用 rpx 作为单位，小程序本身就会处理大部分的屏幕适配问题。
 * 因此，之前针对不同 px 宽度的 @media 查询可以被大大简化或移除。
 * 只有在极少数情况下，比如需要在超大屏或超小屏上做完全不同的布局时，才需要保留 @media 查询。
 * 这里的代码保留了部分查询作为示例，但在一个纯 rpx 的工作流中，它们很多都不是必需的。
 */

/* 小屏幕优化 (如 iPhone SE) */
@media (max-width: 375px) {
  .container {
    padding: 24rpx;
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  }

  .course-title,
  .template-title {
    font-size: 30rpx; /* 稍小于大字体 */
  }

  .info-item {
    font-size: 26rpx; /* 稍小于中字体 */
  }

  .actions-container .t-button,
  .batch-buttons .t-button {
    font-size: 24rpx; /* 小字体 */
    padding: 0 16rpx;
    min-width: 100rpx;
    max-width: 140rpx;
    height: 56rpx;
  }
}


/* 批量模式下的卡片样式 */
.course-card.batch-mode {
  border: 2px solid #0052d9;
  box-shadow: 0 4px 12px rgba(0, 82, 217, 0.2);
  transform: translateY(-2px);
}

.course-title-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  overflow: hidden;
}

.course-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.custom-checkbox {
  width: 18px;
  height: 18px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.custom-checkbox.checked {
  background-color: #0052d9;
  border-color: #0052d9;
}

.check-icon {
  color: #ffffff;
}

/* 移除学员确认对话框样式 */
.remove-student-dialog-content {
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #666;
  line-height: 1.6;
}

.remove-student-dialog-content text {
  display: block;
  margin-bottom: 8px;
}

.remove-student-dialog-content .student-name {
  font-weight: bold;
  color: #333;
}

.refund-checkbox-container {
  display: flex;
  align-items: center;
  margin-top: 16px;
}

.refund-checkbox {
  width: 18px;
  height: 18px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  transition: all 0.2s ease;
}

.refund-checkbox.checked {
  background-color: #0052d9;
  border-color: #0052d9;
}

.refund-checkbox .check-icon {
  color: #ffffff;
}

.refund-label {
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #333;
}

/* 倒计时确认对话框样式 */
.countdown-dialog-content {
  font-size: 28rpx; /* 中字体 - 正文内容 */
  color: #666;
  line-height: 1.6;
}

.countdown-dialog-content text {
  display: block;
  margin-bottom: 8px;
}

.countdown-dialog-content .course-name {
  font-weight: bold;
  color: #d9534f;
}

/* 加载指示器样式 */
.loading-indicator {
  text-align: center;
  color: #888;
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.loading-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #0052d9;
  animation: loading-dot-bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) {
  animation-delay: 0s;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes loading-dot-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 到底部提示样式 */
.end-indicator {
  text-align: center;
  color: #b0b0b0;
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  padding: 16px 0;
  letter-spacing: 1px;
}

/* 自定义下拉刷新样式 */
.custom-refresher {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 45px;
  background-color: #f5f5f5;
}

.refresher-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresher-icon {
  font-size: 32rpx; /* 大字体 - 图标 */
  color: #666;
  transition: transform 0.2s ease;
}

.refresher-text {
  font-size: 24rpx; /* 小字体 - 辅助信息 */
  color: #666;
}

/* 回到顶部按钮样式 */
.back-to-today-fab {
  position: fixed;
  right: 32rpx;
  bottom: 120rpx;
  z-index: 100;
}

.back-to-today-fab .t-fab__btn {
  background-color: rgba(0, 0, 0, 0.5) !important;
  color: #ffffff !important;
}

/* 响应式布局调整 */
/* 针对小屏幕进行优化，确保标签栏和按钮在不同尺寸下都能正确显示 */

/* 中等小屏幕优化 (iPhone SE, 小屏Android) */
@media (max-width: 414px) {
  .actions-container {
    /*
     * 在中等小屏幕上进一步限制按钮容器宽度
     * 为搜索图标和间距留出更多空间
     */
    max-width: 65vw;
    gap: 6px; /* 减小按钮间距 */
  }

  .actions-container .t-button {
    /*
     * 优化按钮尺寸以适应中等小屏幕
     * 减小内边距和字体大小，但保持可读性
     */
    padding: 0 10px;
    font-size: 26rpx; /* 稍小于中字体 */
    min-width: 55px; /* 稍微减小最小宽度 */
    max-width: 75px;
  }

  /* 批量操作栏在中等小屏幕的优化 - 单行布局 */
  .batch-actions-bar {
    /*
     * 在中等小屏幕上减小内边距，为内容腾出更多空间
     * 保持单行布局的间距
     */
    padding: 16rpx 20rpx; /* 使用rpx单位 */
    gap: 20rpx; /* 使用rpx单位 */
  }

  .batch-info {
    /*
     * 优化批量信息文字显示
     * 减小字体但保持可读性
     */
    font-size: 26rpx; /* 稍小于中字体 */
  }

  .batch-buttons {
    gap: 12rpx; /* 使用rpx单位 */
  }

  .batch-buttons .t-button {
    /*
     * 批量操作按钮在中等小屏幕的优化
     * 与顶部按钮保持一致的响应式调整
     */
    height: 56rpx; /* 使用rpx单位 */
    padding: 0 16rpx;
    font-size: 26rpx; /* 稍小于中字体 */
    min-width: 100rpx;
    max-width: 140rpx;
  }
}

/* 小屏幕优化 (iPhone SE第一代等) */
@media (max-width: 390px) {
  .custom-top-tabs .t-tabs__item {
    padding: 12px 14px !important; /* 减小水平内边距 */
    font-size: 30rpx !important; /* 稍小于大字体 */
    min-height: 42px; /* 减小最小高度 */
  }

  .actions-container {
    /*
     * 在小屏幕上进一步优化按钮容器
     * 确保即使是最长的按钮文字也能完整显示
     */
    max-width: 60vw;
    gap: 4px; /* 进一步减小间距 */
  }

  .actions-container .t-button {
    /*
     * 小屏幕下的按钮优化
     * 平衡可读性和空间利用率
     */
    padding: 0 8px;
    font-size: 24rpx; /* 小字体 */
    min-width: 50px;
    max-width: 70px;
  }

  /* 批量操作栏在小屏幕的优化 - 单行布局 */
  .batch-actions-bar {
    /*
     * 在小屏幕上进一步减小内边距
     * 最大化可用空间，保持单行布局
     */
    padding: 12rpx 16rpx; /* 使用rpx单位 */
    gap: 16rpx; /* 使用rpx单位 */
  }

  .batch-info {
    /*
     * 小屏幕下的批量信息优化
     * 进一步减小字体
     */
    font-size: 24rpx; /* 小字体 */
  }

  .batch-buttons {
    gap: 8rpx; /* 使用rpx单位 */
  }

  .batch-buttons .t-button {
    /*
     * 小屏幕下的批量操作按钮优化
     * 与顶部按钮保持一致的响应式调整
     */
    height: 52rpx; /* 使用rpx单位 */
    padding: 0 12rpx;
    font-size: 24rpx; /* 小字体 */
    min-width: 90rpx;
    max-width: 120rpx;
  }
}

/* 超小屏幕优化 (iPhone 5/SE第一代等) */
@media (max-width: 375px) {
  .container {
    padding: 8px;
    padding-bottom: calc(8px + env(safe-area-inset-bottom));
  }

  .course-card,
  .template-card {
    padding: 12px;
  }

  .course-title,
  .template-title {
    font-size: 30rpx; /* 稍小于大字体 */
  }

  .info-item {
    font-size: 26rpx; /* 稍小于中字体 */
  }

  .booking-tab {
    font-size: 26rpx; /* 稍小于中字体 */
    padding: 6px 4px;
  }

  .actions-container {
    /*
     * 超小屏幕的极限优化
     * 确保在最小的屏幕上也能正常显示
     */
    max-width: 55vw;
    gap: 3px; /* 最小间距 */
  }

  .actions-container .t-button {
    /*
     * 超小屏幕下的按钮设置
     * 在保持可用性的前提下最大化空间利用
     */
    font-size: 22rpx; /* 小于小字体 */
    padding: 0 6px;
    min-width: 45px;
    max-width: 65px;
    height: 30px; /* 稍微减小高度 */
  }

  /* 批量操作栏在超小屏幕的极限优化 - 单行布局 */
  .batch-actions-bar {
    /*
     * 超小屏幕的极限优化
     * 最小化内边距，最大化可用空间，保持单行布局
     */
    padding: 8rpx 12rpx; /* 使用rpx单位 */
    gap: 12rpx; /* 使用rpx单位 */
  }

  .batch-info {
    /*
     * 超小屏幕下的批量信息极限优化
     * 保持信息可读的前提下最小化占用空间
     */
    font-size: 22rpx;
  }

  .batch-buttons {
    gap: 6rpx; /* 使用rpx单位 */
  }

  .batch-buttons .t-button {
    /*
     * 超小屏幕下的批量操作按钮极限优化
     * 与顶部按钮保持一致的响应式调整
     */
    height: 48rpx; /* 使用rpx单位 */
    padding: 0 10rpx;
    font-size: 22rpx;
    min-width: 80rpx;
    max-width: 110rpx;
  }

  .custom-top-tabs .t-tabs__item {
    padding: 12px 12px !important; /* 进一步减小内边距 */
    font-size: 15px !important;
  }
}